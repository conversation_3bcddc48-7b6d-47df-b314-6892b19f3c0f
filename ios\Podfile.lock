PODS:
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - image_gallery_saver (1.5.0):
    - Flutter
  - image_picker (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.0.2):
    - Flutter
  - share_files_and_screenshot_widgets (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_ios (0.0.1):
    - Flutter
  - social_share (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker (from `.symlinks/plugins/image_picker/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_files_and_screenshot_widgets (from `.symlinks/plugins/share_files_and_screenshot_widgets/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - social_share (from `.symlinks/plugins/social_share/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - FMDB
    - Toast

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker:
    :path: ".symlinks/plugins/image_picker/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_files_and_screenshot_widgets:
    :path: ".symlinks/plugins/share_files_and_screenshot_widgets/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  social_share:
    :path: ".symlinks/plugins/social_share/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  fluttertoast: 16fbe6039d06a763f3533670197d01fc73459037
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  image_picker: 541dcbb3b9cf32d87eacbd957845d8651d6c62c3
  package_info_plus: 6c92f08e1f853dc01228d6f553146438dafcd14e
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  permission_handler_apple: d21b38e1a4b2e041c63af9568f9165e114e507a6
  share_files_and_screenshot_widgets: 11063d06810f086eade3c1170946f8599e276adb
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  social_share: 702a5e3842addd22db515aa9e1e00a4b80a0296d
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: 839c58cdb4279282219f5e248c3321761ff3c4de
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: 005fbd90c888a42c5690919a1527ecc6649e1162

PODFILE CHECKSUM: 7368163408c647b7eb699d0d788ba6718e18fb8d

COCOAPODS: 1.11.2
