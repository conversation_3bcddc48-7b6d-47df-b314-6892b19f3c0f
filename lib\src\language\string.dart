import 'package:get/get.dart';

class LocaleString extends Translations {
  @override
  // TODO: implement keys
  Map<String, Map<String, String>> get keys => {
        'en_US': {
          'selectLanguage': 'Select Language',
          //hare is Login content Start
          "login": "Login",
          "userid": "User Id / Email",
          "loginpassword": "Password",
          "forgotPassword": "Forgot Password",
          "dontAccount": "Don\'t Have An Account ?",
          // verification account
          "verificationAccount": "Verify Account",
          // Register
          "signUp": "Signup",
          "register": "Register",
          "submit": "Submit",
          "entername": "Enter Name",
          "entermobile": "Enter Mobile",
          "confirmPass": "Confirm Password",
          "ihave": "I Have Carefully Read ",
          "theService": "The Service\nAnd Agreement",
          "and": " And",
          "userprivcy": " Uer Privacy Policies",
          // Tabbar
          "home": "Home",
          "quantitative": "Quantitative",
          "circle": "Circle",
          "news": "News",
          "mine": "Mine",
          // HomePage
          "topnews": "Top News",
          "api_bindige": "API Binding",
          "revenue_detail": "Revenue Detail",
          "reward_detail": "Reward\n  Detail",
          "makePayment": "  Make\nPayment",
          'transation': 'Transaction',
          'video': 'Videos',
          "user_guide": "User Guide",
          "invite_friend": "Invite Friend",
          "openTrade": "Open Trades",
          "hot": "Hot",
          "24h_vol": "24h vol",
          // Tab bar quantitative
          "all": "All",
          "oneshot": "One Shot",
          "stopMarginCall": "Stop Margin Call",
          "searchcurrancyname": "Search Currency Name",
          "start": "Start",
          "pause": "Pause",
          "cycle": "Cycle",
          "startmargincall": "Start margin call",
          "cancelbot": "Cancel Bot",
          'opration_reminder': 'Operation Reminder',
          'opration_dec':
              "Do not operate the Trust-Coin App and the Exchange account at the same 􏰀me. Verify fixed deposit, freezing, enable Reading, enable Spot & Margin Trading and other related se􏰂ngs to prevent system errors affec􏰀ng your rights and interests.",
          'first_preset_by_iin_price': 'First Preset Buy In Price',
          'first_buy_in_amount': 'First Buy In Amount',
          'take_profit_ratio': 'Take Profit Ratio',
          'margin_call_in_limit': "Margin Call In Limit",
          'earning_callback': 'Earning Call Back',
          'margin_call_drop': 'Margin Call Drop',
          'but_in_callback': 'Buy In Callback',
          'sell': 'Sell',
          'buy': 'Buy',
          // trade setting
          'position_amount': 'Position Amt',
          'avgprice': 'AVG Price',
          'number_off_call_margin': 'Number of call margin',
          'position_quantity': 'Position qty',
          'current_price': 'Current Price',
          'return_rate': 'Return rate',
          "trade_setting": "Trade Setting",
          'strategy_mode': 'Strategy Mode',
          // circle
          "createCircle": "Create Circle",
          "income": "Income",
          "tradeSetting": "Trade Setting",
          // mine
          'mamber_center': 'Member Centre',
          "assets": "Assets",
          'transation_record': 'Transaction Record',
          'team': 'Team',
          "teamincome": "Team Income",
          'share': 'Share',
          'support': 'Support',
          'systemSetting': 'System setting',
          'logout': 'Logout',
          // profile info
          'personalinfo': "Personal Info",
          "nickname": "Nickname",
          "uuid": "UUID",
          "rank": "Rank",
          "sponsor": "Sponsor",
          "avatar": "Upload image",
          "email": "Email",
          "location": "Location",
          "changePassword": "Change Password",
          // mamber center
          "securetradeai": "Trustcoin",
          'vip_mamver_rights': 'VIP membership rights',
          'direct_recommend': 'Profit on Your Investment.',
          'robot_activation_direct': 'Returns up to 50% to 80% Monthly.',
          'team_reward': 'Direct Team Trading Profit.',
          'profit_distrubution':
              'Earn 10% trading profit from your direct sponsor',
          'mamber_uses': 'Member User Rules',
          '1':
              '1. Directly push user Profit to deduct the positive part of fuel consumption. and directly push member to purchase activation code.',
          '2': '2. The final interpretation right belongs to the company.',
          // Assets
          "totalassets": "Total Assets",
          "totalincome": 'Total Income',
          'deposit': 'Deposit',
          'withdrawal': 'Withdrawal',
          'transfer': 'Transfer',
          // Depost
          "price": "Price",
          "amount": "Amount",
          "enteramount": "Enter Amount",
          "netamount": "Net Token",
          "haveupaid": "Have You Paid",
          "confirmTransaction": "Confirm Transaction Sharing Hash Key",
          // withdrawal
          'balance': "Balance",
          'currency': 'Currency',
          'address': 'Address',
          // api binding
          'notice': 'Notice',
          'binanc_notice_1':
              '1. Please confirm that the API permissions have checked "Enable Spot & Margin Trading"',
          'binance_notice_2':
              "2. Please enter the correct API, please do not enter special characters",
          'ip_group_binding': 'IP group binding',
          'if_group_dec':
              "For security, Binance exchange recommends binding the server IP address when creating the API, For users who need to binding the IP address, click Edit permissions in the upper right corner after the API is created, and click on the IP address IP's (Recommended) option, click the IP group to the input box and click ok, after adding click Save in the upper right corner ",
          'copy': 'Copy',
          'api_key': 'API Key',
          'secret_key': 'Secret Key',
          "verificationCode": "Verification Code",
          "ihaveAPIbinding": "I Have Read ",
          "theRisk": "The Risk Notice Carefully",
          'if_group_dec_for_huobi':
              "For security, Huobi exchange recommends binding the server IP address when creating the API, For users who need to binding the IP address, click Edit permissions in the upper right corner after the API is created, and click on the IP address IP's (Recommended) option, click the IP group to the input box and click ok, after adding click Save in the upper right corner ",
          'transactionDetail': 'Transaction Detail',
          // team
          'directteam': 'Direct Team',
          'levelTeam': 'Level Team',
          'rankTeam': 'Rank Team',
          // team income
          'allincome': 'All Income',
          'levelincome': 'Level Income',
          'directIncome': 'Direct Income',
          'universalpool': 'Universal pool Income',
          "profitSharingincome": "Profit-Sharing Income",
          "royalty": "Royalty Income",
          // share
          'copyUrl': 'Copy URL',
          // suppor
          'inbox': 'Inbox',
          'create': 'Create',
          'subject': 'Subject',
          'body': 'Body',
          // system setting
          'version': 'Version',
          'aboutsecuretradeai': 'About Trustcoin',
          'contectus': 'Contact Us',
          'language': 'Language',
          'today_s_profit': "Today's \n Profit(USDT)",
          'cumulative_profit': 'Cumulative \n Profit(USDT)',
        },
        'hi_IN': {
          'selectLanguage': 'भाषा चुने',
          //hare is Login content Start
          "login": "लॉग इन करें",
          "userid": "User आईडी",
          "loginpassword": "पासवर्ड",
          "forgotPassword": "पासवर्ड भूल गए",
          "dontAccount": "खाता नहीं है ?",
          // verification account
          "verificationAccount": "खाता सत्यापित करें",
          // Register
          "signUp": "साइन अप करें",
          "register": "रजिस्टर करें",
          "submit": "प्रस्तुत करना",
          "entername": "नाम दर्ज करें",
          "entermobile": "मोबाइल दर्ज करें",
          "confirmPass": "पासवर्ड की पुष्टि कीजिये",
          "ihave": "मैंने ध्यान से पढ़ा है ",
          "theService": "सेवा\nऔर समझौता",
          "and": " तथा",
          "userprivcy": " उपयोगकर्ता गोपनीयता नीतियां",
          // Tabbar
          "home": "घर",
          "quantitative": "मात्रात्मक",
          "circle": "वृत्त",
          "news": "समाचार",
          "mine": "मेरा",
          // HomePage
          "topnews": "मुख्य समाचार",
          "api_bindige": "एपीआई बाइंडिंग",
          "revenue_detail": "राजस्व विवरण",
          "reward_detail": "इनाम विवरण",
          "makePayment": "  भुगतान करें",
          'transation': 'लेन - देन',
          'video': 'वीडियो',
          "user_guide": "उपयोगकर्ता गाइड",
          "invite_friend": "मित्र को न्योता",
          "openTrade": "खुले व्यापार",
          "hot": "गर्म",
          "24h_vol": "24h वॉल्यूम",
          // Tab bar quantitative
          "all": "सभी",
          "oneshot": "एक बार में",
          "stopMarginCall": "मार्जिन कॉल बंद करो",
          // Tab bar quantitative
          "searchcurrancyname": "मुद्रा का नाम खोजें",
          "start": "शुरू",
          "pause": "ठहराव",
          "cycle": "चक्र",
          "startmargincall": "मार्जिन कॉल शुरू करें",
          "cancelbot": "रद्द करें Bot",
          'opration_reminder': 'ऑपरेशन रिमाइंडर',
          'opration_dec':
              "जब Trustcoin प्रो काम कर रहा हो, तो कृपया अपने आप से मुद्रा खाते का संचालन न करें और जांचें कि क्या कोई सावधि जमा, फ्रीजिंग और अन्य संबंधित सेटिंग्स हैं, ताकि सिस्टम के कारण होने वाले असामान्य निर्णयों से बचा जा सके और आपके अधिकार और हितों को प्रभावित किया जा सके।",
          'first_preset_by_iin_price': 'कीमत में पहला प्रीसेट खरीदें',
          'first_buy_in_amount': 'राशि में पहले खरीदें',
          'take_profit_ratio': 'लाभ अनुपात लें',
          'margin_call_in_limit': "सीमा में मार्जिन कॉल",
          'earning_callback': 'अर्निंग कॉल बैक',
          'margin_call_drop': 'मार्जिन कॉल ड्रॉप',
          'but_in_callback': 'कॉलबैक में खरीदें',
          'sell': 'बेचना',
          'buy': 'खरीदना',
          // trade setting
          'position_amount': 'स्थिति amt',
          'avgprice': 'औसत मूल्य',
          'number_off_call_margin': 'कॉल मार्जिन की संख्या',
          'position_quantity': 'स्थिति मात्रा',
          'current_price': 'मौजूदा कीमत',
          'return_rate': 'वापसी की दर',
          "trade_setting": "व्यापार सेटिंग",
          'strategy_mode': 'रणनीति मोड',
          // circle
          "createCircle": "मंडल बनाएं",
          "income": "आय",
          "tradeSetting": "व्यापार सेटिंग",
          // mine
          'mamber_center': 'मेम्बर सेंटर',
          "assets": "संपत्तियां",
          'transation_record': 'लेनदेन का रिकॉर्ड',
          'team': 'टीम',
          "teamincome": "टीम आय",
          'share': 'साझा करना',
          'support': 'सहायता',
          'systemSetting': 'तंत्र समायोजन',
          'logout': 'लॉग आउट',
          // profile info
          'personalinfo': "व्यक्तिगत जानकारी",
          "nickname": "उपनाम",
          "uuid": "यूयूआईडी",
          "rank": "पद",
          "sponsor": "प्रायोजक",
          "avatar": "तस्वीर डालिये",
          "email": "ईमेल",
          "location": "स्थान",
          "changePassword": "पासवर्ड बदलें",
          // mamber center
          "securetradeai": "Trustcoin",
          'vip_mamver_rights': 'वीआईपी सदस्यता अधिकार',
          'direct_recommend': 'प्रत्यक्ष अनुशंसा पुरस्कार',
          'robot_activation_direct': 'रोबोट सक्रियण प्रत्यक्ष बोनस 40%।',
          'team_reward': 'टीम पुरस्कार',
          'profit_distrubution': '12 लेवल डीप से लाभ वितरण।',
          'mamber_uses': 'सदस्य उपयोगकर्ता नियम',
          '1':
              '1. ईंधन की खपत के सकारात्मक हिस्से में कटौती करने के लिए सीधे उपयोगकर्ता लाभ को धक्का दें। और सक्रियण कोड खरीदने के लिए सदस्य को सीधे धक्का दें।',
          '2': '2. अंतिम व्याख्या का अधिकार कंपनी का है।',
          // Assets
          "totalassets": "कुल संपत्ति",
          'deposit': 'जमा',
          'withdrawal': 'निकासी',
          'transfer': 'स्थानांतरण',
          // Depost
          "price": "कीमत",
          "amount": "राशि",
          "enteramount": "राशी डालें",
          "netamount": "शुद्ध टोकन",
          "haveupaid": "क्या आपने भुगतान कर दिया",
          "confirmTransaction": "लेन-देन साझा करने की पुष्टि करें हैश कुंजी",
          // withdrawal
          'balance': "संतुलन",
          'currency': 'मुद्रा',
          'address': 'पता',
          // api binding
          'notice': 'सूचना',
          'binanc_notice_1':
              '1. कृपया पुष्टि करें कि एपीआई अनुमतियों ने "स्पॉट और मार्जिन ट्रेडिंग सक्षम करें" चेक किया है।',
          'binance_notice_2':
              "2. कृपया सही एपीआई दर्ज करें, कृपया विशेष वर्ण दर्ज न करें",
          'ip_group_binding': 'आईपी ​​समूह बाध्यकारी',
          'if_group_dec':
              "सुरक्षा के लिए, बिनेंस एक्सचेंज एपीआई बनाते समय सर्वर आईपी पते को बाध्य करने की सिफारिश करता है, उन उपयोगकर्ताओं के लिए जिन्हें आईपी पते को बाध्य करने की आवश्यकता होती है, एपीआई बनने के बाद ऊपरी दाएं कोने में अनुमतियां संपादित करें पर क्लिक करें, और आईपी पते आईपी (अनुशंसित) पर क्लिक करें। विकल्प, इनपुट बॉक्स में आईपी समूह पर क्लिक करें और ठीक क्लिक करें, जोड़ने के बाद ऊपरी दाएं कोने में सहेजें पर क्लिक करें ",
          'copy': 'प्रतिलिपि',
          'api_key': 'एपीआई कुंजी',
          'secret_key': 'गुप्त कुंजी',
          "verificationCode": "पुष्टि संख्या",
          "ihaveAPIbinding": "मैंने पढ़ा है ",
          "theRisk": "जोखिम नोटिस सावधानी से",
          'if_group_dec_for_huobi':
              "सुरक्षा के लिए, हुओबी एक्सचेंज एपीआई बनाते समय सर्वर आईपी पते को बाध्य करने की सिफारिश करता है, उन उपयोगकर्ताओं के लिए जिन्हें आईपी पते को बाध्य करने की आवश्यकता होती है, एपीआई बनने के बाद ऊपरी दाएं कोने में अनुमतियां संपादित करें पर क्लिक करें, और आईपी पते आईपी (अनुशंसित) पर क्लिक करें। विकल्प, इनपुट बॉक्स में आईपी समूह पर क्लिक करें और ठीक क्लिक करें, जोड़ने के बाद ऊपरी दाएं कोने में सहेजें पर क्लिक करें",
          'transactionDetail': 'लेन-देन विवरण',
          // team
          'directteam': 'सीधी टीम',
          'levelTeam': 'स्तर की टीम',
          'rankTeam': 'रैंक टीम',
          // team income
          'allincome': 'सभी आय',
          'levelincome': 'स्तर आय',
          'directIncome': 'प्रत्यक्ष आय',
          'universalpool': 'यूनिवर्सल पूल आय',
          "profitSharingincome": "लाभ-साझा आय",
          "royalty": "रॉयल्टी आय",
          // share
          'copyUrl': 'यूआरएल कॉपी करें',
          // suppor
          'inbox': 'इनबॉक्स',
          'create': 'सृजन करना',
          'subject': 'विषय',
          'body': 'शरीर',
          // system setting
          'version': 'संस्करण',
          'aboutsecuretradeai': 'Trustcoin के बारे में',
          'contectus': 'संपर्क करें',
          'today_s_profit': "आज का \n लाभ (USDT)",
          'cumulative_profit': 'संचयी \n लाभ (यूएसडीटी)',
          'language': 'भाषा',
        },
        'ar_AR': {
          'selectLanguage': 'اختر اللغة',
          //hare is Login content Start
          "login": "تسجيل الدخول",
          "userid": "عنوان الايميل",
          "loginpassword": "كلمة المرور",
          "forgotPassword": "هل نسيت كلمة السر",
          "dontAccount": "ليس لديك حساب؟",
          // verification account
          "verificationAccount": "التحقق من الحساب",
          // Register
          "signUp": "مشاركة",
          "register": "يسجل",
          "submit": "يقدم",
          "entername": "أدخل الاسم",
          "entermobile": "أدخل الموبايل",
          "confirmPass": "تأكيد كلمة المرور",
          "ihave": "لقد قرأت بعناية ",
          "theService": "الخدمة والاتفاقية",
          "and": " و ال",
          "userprivcy": " سياسات خصوصية المستخدم",
          // Tabbar
          "home": "الصفحة الرئيسية",
          "quantitative": "كمي",
          "circle": "دائرة",
          "news": "أخبار",
          "mine": "الخاص بي",
          // HomePage
          "topnews": "الاخبار المهمه",
          "api_bindige": "API ملزم",
          "revenue_detail": "تفاصيل الإيرادات",
          "reward_detail": "مكافأة \n التفاصيل",
          "makePayment": "  قم بالدفع",
          'transation': 'عملية تجارية',
          'video': 'أشرطة فيديو',
          "user_guide": "دليل المستخدم",
          "invite_friend": "ادعُ صديقًا",
          "openTrade": "التداولات المفتوحة",
          "hot": "حار",
          "24h_vol": "24 ساعة المجلد",
          // Tab bar quantitative
          "all": "الجميع",
          "oneshot": "ضربة واحدة",
          "stopMarginCall": "وقف نداء الهامش",
          "searchcurrancyname": "البحث عن اسم العملة",
          "start": "يبدأ",
          "pause": "وقفة",
          "cycle": "دورة",
          "startmargincall": "ابدأ نداء الهامش",
          "cancelbot": "Cancel بوت",
          'opration_reminder': 'تذكير العملية',
          'opration_dec':
              "عندما يعمل Trustcoin ، يرجى عدم تشغيل حساب العملة بنفسك والتحقق مما إذا كان هناك وديعة ثابتة وتجميد وإعدادات أخرى ذات صلة ، وذلك لتجنب الأحكام غير الطبيعية التي يسببها النظام والتي تؤثر على حقوقك ومصالحك.",
          'first_preset_by_iin_price': 'أول شراء بسعر محدد مسبقًا',
          'first_buy_in_amount': 'أول شراء في المبلغ',
          'take_profit_ratio': 'نسبة أخذ الربح',
          'margin_call_in_limit': "نداء الهامش في الحد",
          'earning_callback': 'كسب الاتصال',
          'margin_call_drop': 'انخفاض نداء الهامش',
          'but_in_callback': 'شراء في رد الاتصال',
          'sell': 'باع',
          'buy': 'يشتري',
          // trade setting
          'position_amount': 'موقف amt',
          'avgprice': 'سعر AVG',
          'number_off_call_margin': 'عدد هامش المكالمة',
          'position_quantity': 'موقف الكمية',
          'current_price': 'السعر الحالي',
          'return_rate': 'معدل العائد',
          "trade_setting": "إعداد التجارة",
          'strategy_mode': 'وضع الإستراتيجية',
          // circle
          "createCircle": "إنشاء دائرة",
          "income": "دخل",
          "tradeSetting": "إعداد التجارة",
          // mine
          'mamber_center': 'مركز الأعضاء',
          "assets": "أصول",
          'transation_record': 'سجل المعاملات',
          'team': 'فريق',
          "teamincome": "دخل الفريق",
          'share': 'يشارك',
          'support': 'الدعم',
          'systemSetting': 'اعدادات النظام',
          'logout': 'تسجيل خروج',
          // profile info
          'personalinfo': "معلومات شخصية",
          "nickname": "اسم الشهرة",
          "uuid": "UUID",
          "rank": "مرتبة",
          "sponsor": "كفيل",
          "avatar": "تحميل الصور",
          "email": "بريد الالكتروني",
          "location": "موقع",
          "changePassword": "تغيير كلمة المرور",
          // mamber center
          "securetradeai": "Trustcoin",
          'vip_mamver_rights': 'حقوق عضوية VIP',
          'direct_recommend': 'جائزة التوصية المباشرة',
          'robot_activation_direct': 'مكافأة تنشيط الروبوت المباشر 40٪.',
          'team_reward': 'مكافآت الفريق',
          'profit_distrubution': 'توزيع الأرباح من 12 مستوى عميق.',
          'mamber_uses': 'قواعد المستخدم العضو',
          '1':
              '1. ادفع أرباح المستخدم مباشرة لخصم الجزء الإيجابي من استهلاك الوقود. ودفع الأعضاء مباشرة لشراء رمز التفعيل.',
          '2': '2. يعود حق التفسير النهائي للشركة.',
          // Assets
          "totalassets": "إجمالي الأصول",
          'deposit': 'الوديعة',
          'withdrawal': 'انسحاب',
          'transfer': 'تحويل',
          // Depost
          "price": "कीमत",
          "amount": "مقدار",
          "enteramount": "أدخل المبلغ",
          "netamount": "Net Token",
          "haveupaid": "هل دفعت",
          "confirmTransaction": "تأكيد مفتاح تجزئة مشاركة المعاملة",
          // withdrawal
          'balance': "الرصيد",
          'currency': 'عملة',
          'address': 'عنوان',
          // api binding
          'notice': 'يلاحظ',
          'binanc_notice_1':
              '1. يرجى تأكيد أن أذونات API قد حددت "تمكين التداول الفوري والهامش"',
          'binance_notice_2':
              "2. الرجاء إدخال API الصحيح ، من فضلك لا تدخل أحرف خاصة",
          'ip_group_binding': 'ربط مجموعة IP',
          'if_group_dec':
              "للأمان ، توصي Binance exchange بربط عنوان IP للخادم عند إنشاء واجهة برمجة التطبيقات ، بالنسبة للمستخدمين الذين يحتاجون إلى ربط عنوان IP ، انقر فوق تحرير الأذونات في الزاوية اليمنى العليا بعد إنشاء واجهة برمجة التطبيقات ، وانقر فوق عنوان IP الخاص بعنوان IP (موصى به) الخيار ، انقر فوق مجموعة IP في مربع الإدخال وانقر فوق موافق ، بعد الإضافة انقر فوق حفظ في الزاوية اليمنى العليا ",
          'copy': 'ينسخ',
          'api_key': 'مفتاح API',
          'secret_key': 'المفتاح السري',
          "verificationCode": "شيفرة التأكيد",
          "ihaveAPIbinding": "لقد قرأت ",
          "theRisk": "إشعار المخاطر بعناية",
          'if_group_dec_for_huobi':
              "للأمان ، توصي Huobi exchange بربط عنوان IP للخادم عند إنشاء واجهة برمجة التطبيقات ، بالنسبة للمستخدمين الذين يحتاجون إلى ربط عنوان IP ، انقر فوق تحرير الأذونات في الزاوية اليمنى العليا بعد إنشاء واجهة برمجة التطبيقات ، وانقر فوق عنوان IP الخاص بعنوان IP (موصى به) الخيار ، انقر فوق مجموعة IP إلى مربع الإدخال وانقر فوق موافق ، بعد الإضافة انقر فوق حفظ في الزاوية اليمنى العليا ",
          'transactionDetail': 'تفاصيل الصفقة',
          // team
          'directteam': 'فريق مباشر',
          'levelTeam': 'فريق المستوى',
          'rankTeam': 'فريق الترتيب',
          // team income
          'allincome': 'كل الدخل',
          'levelincome': 'مستوى الدخل',
          'directIncome': 'الدخل المباشر',
          'universalpool': 'الدخل المجمع الشامل',
          "profitSharingincome": "دخل تقاسم الربح",
          "royalty": "دخل الملوك",
          // share
          'copyUrl': 'إنسخ الرابط',
          // suppor
          'inbox': 'صندوق الوارد',
          'create': 'خلق',
          'subject': 'موضوعات',
          'body': 'الجسم',
          // system setting
          'version': 'الإصدار',
          'aboutsecuretradeai': 'Trustcoin',
          'contectus': 'اتصل بنا',
          'language': 'لغة',
          'today_s_profit': "ربح اليوم (USDT)",
          'cumulative_profit': 'الربح \n التراكمي (USDT)',
        },
      };
}
