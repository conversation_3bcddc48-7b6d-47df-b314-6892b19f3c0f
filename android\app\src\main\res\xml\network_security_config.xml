<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">securetradeai.com</domain>
        <domain includeSubdomains="true">api.binance.com</domain>
        <domain includeSubdomains="true">api.huobi.pro</domain>
        <domain includeSubdomains="true">api.coingecko.com</domain>
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
    
    <!-- Allow all HTTPS traffic -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
