name: securetradeai
description: Secure Trade Ai - Crypto Trading App

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 4.0.0+4

environment:
  sdk: ">=2.12.0 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  shared_preferences: ^2.2.2
  workmanager: ^0.5.1  # Updated to a more stable version
  flutter_local_notifications: ^11.0.0  # Added for background notifications

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  carousel_slider: any
#  animated_text_kit: ^4.2.1
  flutter_svg: ^0.22.0  # Updated to be compatible with flutter_html
  shimmer: ^3.0.0
  barcode_widget: ^2.0.4
  charts_flutter: ^0.12.0
  syncfusion_flutter_charts: ^21.2.4
  get: ^4.3.8
  country_picker: ^2.0.11
  http: ^0.13.0  # Downgraded to be compatible with package_info_plus
  # fluttertoast: ^8.0.8
  toast: any
  timer_builder: ^2.0.0
  flutter_html: ^2.1.5  # Using an older version compatible with SDK
  image_picker: ^1.0.4
  package_info_plus: ^1.4.2  # Downgraded to a compatible version
  webview_flutter: ^2.0.13  # Compatible version with flutter_html
  # social_share:
  url_launcher: ^6.0.20  # Downgraded to be compatible with Dart SDK 2.19.6
  video_player: ^2.7.2  # Compatible with Dart SDK 2.19.6
  flutter_speed_dial: ^7.0.0
  # image_gallery_saver: ^1.7.1
  permission_handler: ^8.3.0  # Downgraded to be compatible with SDK
  share_files_and_screenshot_widgets: ^1.0.6
  email_validator: ^2.0.1
  provider: ^6.0.5
  like_button: ^2.0.4
  cached_network_image: ^2.5.1  # OPTIMIZATION: Compatible version for current Dart SDK
  pull_to_refresh: ^2.0.0
  dropdown_button2: ^2.1.4
  share_plus: ^4.5.3  # Downgraded to be compatible with SDK
  dotted_border: ^2.0.0  # Fixed version format
  slider_captcha: ^1.0.0
  equatable: ^2.0.3
  expansion_tile_card: ^3.0.0
  youtube_player_flutter: ^8.1.0
  chewie: ^1.5.0
  # upgrader:
  open_store: ^0.5.0
  flutter_plugin_android_lifecycle: ^2.0.16
  intl: ^0.17.0
  web_socket_channel : any
  youtube_player_iframe: any
  # Export functionality
  pdf: ^3.8.4
  excel: ^2.0.4
  path_provider: ^2.0.11

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/img/
    - assets/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Ubuntu
      fonts:
        - asset: assets/fonts/Ubuntu-Regular.ttf
        - asset: assets/fonts/Ubuntu-Bold.ttf
    - family: Nunito
      fonts:
        - asset: assets/fonts/Nunito-Regular.ttf
        - asset: assets/fonts/Nunito-Bold.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
