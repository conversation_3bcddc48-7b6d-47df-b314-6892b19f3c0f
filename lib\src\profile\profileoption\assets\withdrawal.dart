import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:securetradeai/Data/Api.dart';
import 'package:securetradeai/data/strings.dart';
import 'package:securetradeai/src/Service/assets_service.dart';
import 'package:securetradeai/src/widget/common_app_bar.dart';

class Withdrawal extends StatefulWidget {
  const Withdrawal({Key? key, this.balance}) : super(key: key);
  final balance;
  @override
  _WithdrawalState createState() => _WithdrawalState();
}

class _WithdrawalState extends State<Withdrawal> {
  var address = TextEditingController();
  var amount = TextEditingController();
  var transpass = TextEditingController();
  var pttnTokenvalue = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        appBar: CommonAppBar.basic(
          title: 'withdrawal'.tr,
        ),
        body: Padding(
          padding: const EdgeInsets.only(left: 20.0, right: 10, top: 10),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        child: Text(
                          "balance".tr + " : ",
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18),
                        ),
                      ),
                      Container(
                        child: Text(
                          widget.balance.toString() + " USDT",
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16),
                        ),
                      )
                    ]),
                SizedBox(
                  height: 20,
                ),
                // Row(
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     children: [
                //       Container(
                //         child: Text(
                //           "currency".tr + " : ",
                //           style: TextStyle(
                //               color: Colors.white,
                //               fontWeight: FontWeight.bold,
                //               fontSize: 18),
                //         ),
                //       ),
                //       Container(
                //         child: const Text(
                //           " USD(TRC-20)",
                //           style: const TextStyle(
                //               color: Colors.white,
                //               fontWeight: FontWeight.bold,
                //               fontSize: 16),
                //         ),
                //       )
                //     ]),
                // const SizedBox(
                //   height: 20,
                // ),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        child: Text(
                          "address".tr + " :     ",
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18),
                        ),
                      ),
                      Expanded(
                        child: Container(
                            child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                              border: Border.all(
                                color: Color(0xfff3f3f4),
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: TextField(
                                style: const TextStyle(color: Colors.white),
                                controller: address,
                                decoration: const InputDecoration(
                                  hintStyle: TextStyle(
                                      color: Colors.white70, fontSize: 13),
                                  hintText: "0xFa234...",
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                ),
                                onChanged: (v) {
                                  setState(() {
                                    // netamount = (v == null || v == "")
                                    //     ? 0.0
                                    //     : double.parse(v);
                                  });
                                },
                              ),
                            ),
                          ),
                        )),
                      )
                    ]),
                const SizedBox(
                  height: 20,
                ),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        child: Text(
                          "amount".tr + r"($) : ",
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18),
                        ),
                      ),
                      Expanded(
                        child: Container(
                            child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                              border: Border.all(
                                color: Color(0xfff3f3f4),
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: TextField(
                                style: const TextStyle(color: Colors.white),
                                controller: amount,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  hintStyle: TextStyle(
                                      color: Colors.white70, fontSize: 13),
                                  hintText: "amount".tr + "(USDT)",
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                ),
                              ),
                            ),
                          ),
                        )),
                      )
                    ]),
                const SizedBox(
                  height: 20,
                ),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        child: Text(
                          "loginpassword".tr + " : ",
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18),
                        ),
                      ),
                      Expanded(
                        child: Container(
                            child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                              border: Border.all(
                                color: Color(0xfff3f3f4),
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: TextField(
                                obscureText: true,
                                style: const TextStyle(color: Colors.white),
                                controller: transpass,
                                decoration: InputDecoration(
                                  hintStyle: TextStyle(
                                      color: Colors.white70, fontSize: 13),
                                  hintText: "loginpassword".tr,
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                ),
                              ),
                            ),
                          ),
                        )),
                      )
                    ]),

                SizedBox(
                  height: 40,
                ),
                InkWell(
                  onTap: () {
                    _checkWithdrawalAbleOrNot();
                  },
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.symmetric(vertical: 15),
                    alignment: Alignment.center,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0.0, 1.0],
                        colors: [
                          Colors.green,
                          Colors.blue,
                        ],
                      ),
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                      boxShadow: const <BoxShadow>[
                        BoxShadow(
                            color: Colors.black12,
                            offset: Offset(2, 4),
                            blurRadius: 5,
                            spreadRadius: 2)
                      ],
                    ),
                    child: Text(
                      "withdrawal".tr,
                      style: TextStyle(fontSize: 20, color: Colors.white),
                    ),
                  ),
                )
              ],
            ),
          ),
        ));
  }

  _checkWithdrawalAbleOrNot() async {
    try {
      showLoading(context);
      if (address.text == "") {
        showtoast("Address Field is Empty", context);
        Navigator.pop(context);
      } else if (amount.text == "") {
        showtoast("Amount Field is Empty", context);
        Navigator.pop(context);
      } else if (transpass.text == "") {
        showtoast("Transaction Paaword Field is Empty", context);
        Navigator.pop(context);
      } else {
        final res = await http.post(Uri.parse(getIp),
            body: json.encode({"user_id": commonuserId}));
        if (res.statusCode == 200) {
          var data = jsonDecode(res.body);
          if (data['status'] == "success") {
            if (double.parse(amount.text) <
                double.parse(data['data']['admin_details']['min_withdraw'])) {
              showtoast(
                  "Min Withdrawal value ${data['data']['admin_details']['min_withdraw']}",
                  context);
              Navigator.pop(context);
            } else {
              _withdrawal();
              Navigator.pop(context);
            }
          }
        } else {
          showtoast("Server Error", context);
          Navigator.pop(context);
        }
      }
    } catch (e) {
      print(e);
    }
  }

  Future _withdrawal() async {
    try {
      showLoading(context);
      var bodydata = jsonEncode({
        "user_id": commonuserId,
        "amount": amount.text,
        "qty": "0.00",
        "address": address.text,
        "password": transpass.text
      });
      print(bodydata);
      final resp = await http.post(Uri.parse(withdrawal), body: bodydata);
      if (resp.statusCode != 200) {
        showtoast("Server Error", context);
        Navigator.pop(context);
      } else {
        var jsondata = jsonDecode(resp.body);
        print(jsondata);
        if (jsondata['status'] == "success") {
          showtoast(jsondata['message'], context);
          address.clear();
          amount.clear();
          transpass.clear();
          pttnTokenvalue.clear();
          Navigator.pop(context);
        } else {
          showtoast(jsondata['message'], context);
          Navigator.pop(context);
        }
        // if(jsondata)
      }
    } catch (e) {
      Navigator.pop(context);
      print(e);
    }
  }
}
